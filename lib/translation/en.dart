final enUS = {
  ///===========common================
  "confirm": "Confirm",
  "cancel": "Cancel",
  "back": "back",
  "paste": "Paste",

  ///===========application===========
  "app_name": "Xiaopa AI",
  "console": "<PERSON>pa",
  "chat": "Cha<PERSON>",
  "diary": "Mood Diary",
  "mine": "Me",
  "exit_app": "Press again to exit the app",

  ///==============base================
  "base_reload": "Reload",
  "base_unkonwn": "Unknown issue, under investigation",
  "base_list_refreshing": "Refreshing…",
  "base_list_complete": "Refresh successful",
  "base_list_failed": "Refresh failed",
  "base_list_idle": "Refreshing",
  "base_list_down_refresh": "Pull down to refresh",
  "base_list_load_more": "Pull up to load more",
  "base_list_load_fail": "Load failed",
  "base_list_loading": "Loading…",
  "base_list_load_more_data": "Release to load more",
  "base_list_no_more_data": "No more data",

  ///=============network===============
  "newwork_cancel": "Request canceled",
  "network_client_net_error": "Network error. Please check your connection.",
  "network_server_net_error": "System is busy. Please try again later.",
  "network_net_connect_error": "No network connection. Please check and retry.",
  "network_too_many_request": "Too many requests. Please wait.",

  ///==============splash========================
  "splash_first_title": "Service Agreement and Privacy",
  "splash_first_tips":
      "Please read carefully and fully understand the Software License Agreement and Privacy Policy, including user notices, behavior guidelines, and how your personal information may be used, stored, and shared. For details, read the 'User Agreement' and 'Privacy Policy'. If you agree, tap the button below to start using our services.",
  "splash_first_comfirm": "Agree",
  "splash_first_cancel": "Decline",

  ///=============login======================
  "login_pwd": "Password login",
  "login_verify": "Code login",
  "login_phone": "Phone number",
  "login_phone_hint_text": "Enter your phone number",
  "login_pwd_code": "Password",
  "login_verify_code": "Verification code",
  "login_verify_send": "Send code",
  "login_verify_resend": "Resend",
  "login_pwd_hint_text": "Enter your password",
  "login_verify_hint_text": "Enter the code",
  "login_register": "Register",
  "login_forget_pwd": "Forgot password",
  "login_protocol": "Agree to",
  "login_add": "and",
  "login_protocol_one": "User Agreement",
  "login_protocol_two": "Privacy Policy",
  "login_btn": "Log in",
  "login_error_tips_one": "Please enter your phone number!",
  "login_error_tips_two": "Please enter your password!",
  "login_error_tips_three": "Please enter the verification code!",
  "login_error_tips_four": "Incorrect verification code!",
  "login_error_tips_five":
      "Please agree to the User Agreement and Privacy Policy!",

  "login_select_country": "Select country/region",

  "login_pwd_title": "Change\npassword",

  ///=============register=====================
  "register_user": "Sign up",
  "register_user_tips": "Quick sign-up and \nmeet Xiaopa",
  "register_pwd_hint_text": "8–16 chars, letters & numbers",
  "register_pwd_confirm_hint_text": "Confirm new password",
  "register_btn": "Sign up now",
  "register_back": "Already have an account? Log in →",

  ///=============Xiaopa console=============
  "console_name": "Xiaopa",
  "console_interrupt_mode": "Interrupt mode",
  "console_interrupt_mode_tip":
      "When off, Xiaopa will finish speaking and won't be interrupted by external sounds",
  "console_silence_mode": "Silent mode",
  "console_silence_mode_tip":
      "When on, Xiaopa enters silent sleep mode and will not be awakened, nor will it broadcast task reminders",
  "console_drink_record": "Water log",
  "console_dirk_tips_one": "Had %s cups",
  "console_dirk_tips_two": "Goal %s cups",
  "console_tips": "Tap to start warm companionship",
  "console_add": "Add Xiaopa",
  "console_off": "Off",
  "console_short_one": "Pay raise +1",
  "console_short_two": "Happiness +1",
  "console_short_three": "Luck +1",
  "console_short_four": "Loved +1",
  "console_short_five": "Confidence +1",
  "console_short_six": "Well-being +1",
  "console_short_seven": "Good sleep +1",
  "console_short_eight": "Latte fund +1",
  "console_short_nine": "Understood +1",
  "console_short_ten": "Compliments +1",
  "console_long_one": "Xiaopa is here, never absent",
  "console_long_two": "Tap once, worries vanish",
  "console_long_three": "Ten streaks of luck, steady joy",
  "console_long_four": "The world is noisy—don’t panic",
  "console_long_five": "Surrounded by love, no need to act tough",
  "console_long_six": "Life is worth it, and Xiaopa is here",
  "console_long_seven": "A warm hug for you",
  "console_long_eight": "Every effort will shine",
  "console_long_nine": "Luck loading—keep smiling~",
  "console_long_ten": "Work quietly, amaze everyone",
  "console_offline_tips": "Xiaopa is offline and can’t respond right now~",
  "console_reset_tips": "Changing this setting will restart Xiaopa~",
  "console_online": "Online",
  "console_offline": "Offline",
  "console_xiumian": "sleep",
  "console_jingyin": "mute",
  "console_select": "Choose Xiaopa",
  "console_pairing_step_one": "Step 1: Press the nose to power on",
  "console_pairing_step_two":
      "Step 2: Press the left button under the chin to enter Wi‑Fi setup",
  "console_pairing_before":
      "Before you start, make sure your phone’s Bluetooth is on so Xiaopa can connect",
  "console_pairing_start": "Start searching",
  "console_pairing_one_tip_one": "Scanning for Xiaopa’s Bluetooth",
  "console_pairing_one_tip_two": "Connecting phone to Xiaopa",
  "console_pairing_one_tip_three": "Binding Xiaopa",
  "console_pairing_one_tip_four": "Setting Wi‑Fi",
  "console_pairing_one_error_tip_one": "No Xiaopa Bluetooth device found",
  "console_pairing_one_error_tip_two": "Phone not connected to Xiaopa",
  "console_pairing_one_error_tip_three": "Binding failed",
  "console_pairing_one_error_tip_four": "Wi‑Fi connection failed",
  "console_pairing_two_tip_one":
      "Scanning Xiaopa’s Bluetooth signal… connecting…",
  "console_pairing_two_tip_two": "Scanning Wi‑Fi… phone connecting…",
  "console_pairing_two_tip_three": "Binding Xiaopa…",
  "console_pairing_two_tip_four": "Configuring Wi‑Fi…",
  "console_pairing_error_tip_one":
      "Please enable Bluetooth and Location so Xiaopa can connect to your phone~",
  "console_pairing_error_tip_two":
      "Please turn on Wi‑Fi so Xiaopa can connect to your phone~",
  "console_pairing_error_tip_three":
      "This Xiaopa is already bound. Open ‘Xiaopa → Settings’ in the bound account to unbind first",
  "console_pairing_error_tip_four":
      "Wi‑Fi setup failed. Please enter the correct password~",
  "console_pairing_tip_one": "Bluetooth is connecting to Xiaopa",
  "console_pairing_tip_two": "Phone is connecting to Xiaopa",
  "console_pairing_tip_three": "Xiaopa is binding",
  "console_pairing_tip_four": "Wi‑Fi is being configured",
  "console_devoce_item_tip": "Your Xiaopa",

  "console_device_item_btn": "Connect",
  "console_device_item_btn_one": "Activate",
  "console_device_item_btn_two": "Unbound",
  "console_device_active_title": "Activate Xiaopa?",
  "console_device_active_tip": "Activation counts as use — no 7-day return",
  "console_device_active_btn": "Activate",

  "console_pairing_wifi_title": "Set up Xiaopa’s Wi‑Fi",
  "console_paring_wifi_tips_ios":
      "Xiaopa connects only to 2.4 GHz Wi‑Fi，5 GHz is not supported.",
  "console_paring_wifi_tips_android":
      "To ensure a stable connection, Xiaopa only supports 2.4 GHz Wi‑Fi. 5 GHz is currently not supported.",

  "console_paring_wifi_kown": "Saved Wi‑Fi",
  "console_paring_wifi_delete": "Delete",

  "console_paring_wifi_list": "Wi‑Fi list",
  "console_paring_wifi_refresh_tips":
      "No Wi‑Fi found. Tap the button on the right to refresh",
  "console_paring_wifi_input": "Enter Wi‑Fi name manually",
  "console_paring_wifi_name": "Name",
  "console_paring_wifi_name_hint_text": "Wi‑Fi name",
  "console_paring_wifi_pwd": "Password",
  "console_paring_wifi_pwd_hint_text": "Enter password",
  "console_paring_wifi_next": "Next",

  "console_paring_wifi_start_connect": "Start setup",

  "console_setting_title": "Settings",
  "console_setting_copy": "Copied",

  "console_role": "Character profile",
  "console_role_hint_text":
      "Write down the information you hope Xiaopa will remember, such as: Xiaopa is a cold and aloof panda. He likes to share jokes with me, but sometimes he ignores me",
  "console_role_empty": "Please enter the content you want to modify!",
  "console_role_save": "Save",

  "console_language_title": "Xiaopa language",
  "console_language_tips":
      "Choose the language Xiao Pa will use when talking with you.",
  "console_language_one":
      "The modification will only take effect after restarting the device!",
  "console_language_two": "The device id cannot be empty!",

  "console_compiled_title": "Interaction preferences",

  "console_compiled_today": "Today",
  "console_compiled_tomorrow": "Tomorrow",

  "console_compiled_second_title": "Companionship pace",
  "console_compiled_second_tips": "I’ll keep you company at the pace you like~",
  "console_compiled_second_item_one_title": "accompany silently",
  "console_compiled_second_item_one_tips":
      "I will stay by your side quietly, unless you ask me to accompany you silently",
  "console_compiled_second_item_two_title": "chat occasionally",
  "console_compiled_second_item_two_tips":
      "Occasionally I can't help but open my mouth and talk to you about some small topics",
  "console_compiled_second_item_three_title": "Chat frequently",
  "console_compiled_second_item_three_tips":
      "I talk to you from time to time, and I always want to be closer to you.",
  "console_compiled_second_item_three_tips_two":
      "The frequency of active opening is controlled by the system intelligently, providing companionship without excessive interruption",
  "console_compiled_third_title": "Do Not Disturb",
  "console_compiled_third_tips":
      "When enabled, Xiaopa won’t proactively speak or interrupt",
  "console_compiled_confirm": "Confirm",
  "console_compiled_close": "Off",
  "console_compiled_save": "Save",

  "console_sound_title": "Voice",
  "console_sound_exchange": "Voucher",
  "console_sound_package": "My voice packs",
  "console_sound_tips": "Please clone a voice first!",
  "console_sound_error_tips": "The default system voice pack can’t be deleted",
  "console_sound_price_error_tips": "Pricing error. Please contact support",
  "console_sound_exchange_tips": "Use %s PaPa Coins to redeem?",
  "console_sound_confirm": "Redeem now",
  "console_sound_exchange_success": "Redeemed successfully",
  "console_sound_exchange_tips_one": "10 custom voice uses",
  "console_sound_exchange_tips_date": "Valid for 1 year",
  "console_sound_exchange_btn": "Clone my voice",
  "console_sound_explan_title": "What is Voice Clone?",
  "console_sound_explan_title_one":
      "Voice cloning is AI magic for a customized voice!",
  "console_sound_explan_tips_one":
      "Record a short sample of your voice and Xiaopa will learn it—so it can say what you want in your voice.",
  "console_sound_explan_title_two": "How to use",
  "console_sound_explan_tips_two":
      "1. Record your voice following the prompts\n2. Wait for cloning to finish—your custom voice is ready!\n3. Your recorded voice is used with the Xiaopa companion toy. The in‑app digital avatar currently doesn’t support custom voices",
  "console_sound_explan_title_three": "Required credits:",
  "console_sound_explan_tips_three":
      "1. 10 voice clone chances cost %s PaPa Coins\n2. Each clone consumes 1 chance (10 total)",
  "console_sound_explan_title_four": "Safety notes:",
  "console_sound_explan_tips_four":
      "1. All audio is used only for this feature and will not be shared\n2. Cloned voices belong to you only; only you can view and use them",
  "console_sound_item_name_title": "Name your voice",
  "console_sound_item_name_tips":
      "I’ll speak using this name, e.g., ‘Bunny is here, keeping you company’",
  "console_sound_item_name_hint_text": "e.g., Bunny",
  "console_sound_item_name_hlep_text":
      "Up to %s characters (letters or Chinese)",
  "console_sound_item_name_hlep_text_one": "Please enter Chinese or English",
  "console_sound_item_name_hlep_text_two": "Please enter a voice pack name",
  "console_sound_item_sign": "Default",
  "console_sound_item_permanent_date": "Valid permanently",
  "console_sound_item_date": "Valid until %s",
  "console_sound_item_tips": "Clones left",
  "console_sound_item_tips_count": "%s times",
  "console_sound_item_tips_using": "In use",
  "console_sound_item_tips_use": "Use",
  "console_sound_item_clone": "Clone",
  "console_sound_voice_title": "Voice usage notes",
  "console_sound_voice_title_two":
      "Each custom voice pack provides 10 uses—for recording or switching",
  "console_sound_voice_tips_two":
      "1. Recording a new voice consumes 1 use\n\n2. Switching to ‘Xiaopa default voice’ doesn’t consume uses\n\n3. Switching back to your most recently used custom voice also doesn’t consume uses\n\n4. Your recorded voice is used with the Xiaopa toy. The in‑app digital avatar currently doesn’t support custom voices",

  "console_sound_record": "Voice recording",
  "console_sound_record_title": "Please read",
  "console_sound_record_content":
      "Summer is here—time for watermelon! I love eating it in an air‑conditioned room. It feels amazing!",
  "console_sound_record_error_tips": "Noisy environment. Please re‑record",
  "console_sound_record_stop": "Stop",
  "console_sound_record_listen": "Preview",
  "console_sound_record_compose": "Done",
  "console_sound_record_start_record": "Tap to record",
  "console_sound_record_reset_record": "Re‑record",
  "console_sound_record_stop_record": "Tap to stop",
  "console_sound_record_dialog_tips":
      "Each voice pack has 10 recording chances.\nThis will use 1 chance to generate your exclusive Xiaopa voice~",
  "console_sound_record_dialog_title": "Generate your custom voice?",
  "console_sound_record_dialog_confirm": "Generate",
  "console_sound_record_error_one": "Voice ID error!",
  "console_sound_record_error_two": "Recording upload failed!",
  "console_sound_tips_title_one": "Keep the environment quiet",
  "console_sound_tips_tips_one":
      "Record in a quiet place to reduce background noise and improve quality.",
  "console_sound_tips_title_two": "Use standard pronunciation",
  "console_sound_tips_tips_two":
      "Speak clearly with standard pronunciation for better recognition and playback.",
  "console_sound_tips_title_three": "Mind your speed and volume",
  "console_sound_tips_tips_three":
      "Speak clearly at a moderate speed and volume for the best results.",
  "console_sound_tips_title_four": "Keep a proper distance",
  "console_sound_tips_tips_four":
      "Keep about 10 cm (4 in) from the phone when recording for better quality.",
  "console_sound_tips_title_five": "Preview and adjust",
  "console_sound_tips_tips_five":
      "After each recording, preview it to ensure you’re satisfied. Re‑record if needed.",

  "console_sound_name_dialog_title": "Name your voice",
  "console_sound_name_dialog_tips":
      "I’ll speak using this name, e.g., ‘Bunny is here, keeping you company~’",

  "console_sound_exchange_view_one": "Voucher code",
  "console_sound_exchange_view_hint_text": "Enter the voucher code",
  "console_sound_exchange_view_title": "Friendly reminder",
  "console_sound_exchange_view_tips":
      "1、Each voucher can be redeemed only once.\n2、Use English input and match letter case when entering the code.",
  "console_sound_exchange_view_btn": "Redeem now",
  "console_sound_exchange_view_empty_tips": "Please enter the voucher code!",
  "console_sound_exchange_view_length_tips":
      "Invalid code length. Please check!",
  "console_sound_exchange_view_no_tips": "Voucher code does not exist",
  "console_sound_exchange_fail_title": "Redemption failed",

  "console_update": "Xiaopa update",
  "console_update_auto": "Auto update",
  "console_update_now": "Update now",
  "console_update_current_version": "Current version",
  "console_update_new_version": "Latest version",
  "console_update_version_now": "Device current version",
  "console_update_new_version_now": "Already the latest version",

  "console_clear_title": "Clear the memory",
  "console_clear_one": "Xiaopa is about to get to know you again",
  "console_clear_two":
      "After clearing the memory, Xiaopa will forget all your stories: chat records, nicknames. \nFrom now on, we will rebuild new memories just as we did when we first met。",
  "console_clear_three":
      "This cannot be restored. Please make sure you have made up your mind~",
  "console_clear_btn": "Start a new journey",

  "console_unbind": "Unbind",
  "console_unbind_title": "Unbind Xiaopa?",
  "console_unbind_tips":
      "After unbinding, your partner can bind Xiaopa. Your data will be erased and cannot be recovered.",
  "console_unbind_confirm": "Unbind",
  "console_unbind_success": "Xiaopa unbound successfully—vacation time!",
  "console_unbind_success_tips":
      "Wishing you happiness every day. Call me again anytime~",
  "console_unbind_back": "Back",

  "console_schedule_title": "Schedule",
  "console_schedule_tips_one": "Today:%s tasks",
  "console_schedule_one": "Today",
  "console_schedule_two": "Plan",
  "console_schedule_three": "All",
  "console_schedule_btn": "New Reminder",

  "schedule_add_title": "New Reminder",
  "schedule_add_hint_text_one": "Will be reminded by the Xiao Pa",
  "schedule_add_hint_text_two": "remarks",
  "schedule_add_item_one": "Date",
  "console_schedule_today": "（Today）",
  "schedule_add_item_two": "Time",
  "schedule_add_item_three": "Repeat",
  "schedule_add_item_btn": "Add",
  "schedule_repeat_one": "Never",
  "schedule_repeat_two": "Every Day",
  "schedule_repeat_three": "Working Day",
  "schedule_repeat_four": "Weekend",
  "schedule_repeat_five": "Set at least 3 min~",
  "schedule_repeat_save": "Save",
  "schedule_repeat_tips_one": "Please select the date!",
  "schedule_repeat_tips_two": "Please select the time!",
  "schedule_repeat_tips_three": "The title cannot be empty",
  "schedule_repeat_tips_four": "Please Set at least 3 min",
  "schedule_repeat_tips_five":
      "The reminder content is set incorrectly. Please reset it!",

  "schedule_today_title": "Today's matters",
  "schedule_today_delete": "",
  "schedule_today_btn": "New Reminder",

  "schedule_plan_title": "Planned matters",

  "schedule_all_title": "All matters",
  "schedule_all_del": "Delete",
  "schedule_all_clear": "Clear",
  "schedule_all_completed": "Completed",
  "schedule_all_select": "Select all",

  ///=============chat=======================
  "chat_btn_press": "Hold to talk",
  "chat_btn_release": "Release to end",

  "chat_listening": "Listening",
  "chat_thinking": "Thinking",
  "chat_answering": "Replying",
  "chat_pasued": "Call paused",
  "chat_hint_text": "Say hi to start chatting",

  ///============diary========================
  "diary_record": "Mood log",
  "diary_record_tips": "Be with Xiaopa and let emotions flow",
  "diary_check": "View",

  "diary_type_happy": "Happy",
  "diary_type_good": "Good",
  "diary_type_normal": "Mediocre",
  "diary_type_bad": "Bad",
  "diary_type_very_bad": "Terrible",

  "diary_normal_quotes_one": "Not every day shines, but you keep moving.",
  "diary_normal_quotes_two": "It’s okay to feel stuck. The sun still rises.",
  "diary_normal_quotes_three": "Even a flat mood deserves respect.",
  "diary_normal_quotes_four": "A calm day is a kind of peace.",
  "diary_normal_quotes_five": "An ordinary day can become a good memory.",
  "diary_normal_quotes_six": "You’ve done nothing wrong—you’re breathing.",
  "diary_normal_quotes_seven": "Just as you are now, you’re doing great.",
  "diary_normal_quotes_eight": "No rush. Every step matters.",
  "diary_normal_quotes_nine": "Life flows. You’ll find your way.",
  "diary_normal_quotes_ten": "Imperfect is still a good life.",
  "diary_normal_quotes_eleven": "Even an average day deserves kindness.",
  "diary_normal_quotes_twelve": "Nothing special can still feel grounded.",
  "diary_normal_quotes_thirteen": "Sometimes calm is the biggest blessing.",
  "diary_normal_quotes_fourteen": "Feelings move and slowly recover.",
  "diary_normal_quotes_fifteen": "Today may be plain, but you still matter.",
  "diary_normal_quotes_sixteen":
      "It’s okay—being ordinary is a way to breathe.",
  "diary_normal_quotes_seventeen": "Quiet days can still be warm.",
  "diary_normal_quotes_eighteen": "Keep going. The scenery will change.",
  "diary_normal_quotes_nineteen": "Unhurried and unrushed is a sign of growth.",
  "diary_normal_quotes_twenty": "The way you try to live is moving.",
  "diary_normal_quotes_twenty_one":
      "Not every day is epic—some are in‑between.",
  "diary_normal_quotes_twenty_two": "Some days look ordinary but mean a lot.",
  "diary_normal_quotes_twenty_three": "Not dazzling, but steady and lovely.",
  "diary_normal_quotes_twenty_four": "Ups and downs are part of being real.",
  "diary_normal_quotes_twenty_five":
      "Don’t underestimate calm—it’s a prelude to joy.",
  "diary_normal_quotes_twenty_six": "‘Just okay’ is part of life’s rhythm.",
  "diary_normal_quotes_twenty_seven":
      "It’s fine if today isn’t exciting—steady is good.",
  "diary_normal_quotes_twenty_eight": "Your ordinary is a unique view.",
  "diary_normal_quotes_twenty_nine":
      "Life isn’t always a peak; some valleys are gentle.",
  "diary_normal_quotes_thirty": "Calm and patient is a kind of growth.",
  "diary_normal_quotes_thirty_one": "It’s okay to go slower.",
  "diary_normal_quotes_thirty_two": "A day without disturbances is nice too.",
  "diary_normal_quotes_thirty_three":
      "Seemingly simple days hold many treasures.",
  "diary_normal_quotes_thirty_four": "In quiet, you’re gathering strength.",
  "diary_normal_quotes_thirty_five":
      "No big waves can still be peaceful happiness.",
  "diary_normal_quotes_thirty_six": "Your persistence is change happening.",
  "diary_normal_quotes_thirty_seven":
      "Fatigue is a reminder to be gentle with yourself.",
  "diary_normal_quotes_thirty_eight":
      "You’re not stuck—you’re building momentum.",
  "diary_normal_quotes_thirty_nine": "Believe it: ordinary days can bloom.",
  "diary_normal_quotes_forty": "Patience is the gift hidden in the everyday.",

  "diary_analysis_title": "Mood stats & insights",
  "diary_calendar": "Mood calendar",
  "diary_distribution": "Mood distribution",
  "diary_trend": "Mood trend",

  "diary_date_day": "Day",
  "diary_date_week": "Week",
  "diary_date_month": "Month",
  "diary_date_year": "Year",

  "dary_date_one": "Jan",
  "diary_date_two": "Feb",
  "diary_date_three": "Mar",
  "diary_date_four": "Apr",
  "diary_date_five": "May",
  "diary_date_six": "Jun",
  "diary_date_seven": "Jul",
  "diary_date_eight": "Aug",
  "diary_date_nine": "Sep",
  "diary_date_ten": "Oct",
  "diary_date_eleven": "Nov",
  "diary_date_twelve": "Dec",

  ///==========weekdays===================
  "diary_week_one": "Mon",
  "diary_week_two": "Tue",
  "diary_week_three": "Wed",
  "diary_week_four": "Thu",
  "diary_week_five": "Fri",
  "diary_week_six": "Sat",
  "diary_week_seven": "Sun",

  "diary_record_empty_tips": "Nothing here yet today~",
  "diary_record_item_show": "Expand",
  "diary_record_item_closed": "Collapse",

  "diary_mood_title": "Manage",
  "diart_mood_complete": "Done",
  "diary_mood_empty_tips": "I’m here, anytime you need me~",

  "diary_mood_item_one": "Because",
  "diary_mood_item_two": "I feel",

  "diary_edit_title": "Your feelings",
  "diary_edit_btn": "Edit",
  "diary_edit_btn_complete": "Save this feeling",

  "diary_edit_photo_title": "All photos",
  "diary_edit_photo_edit": "Crop photo",

  "diary_edit_one_title": "Mood now",
  "diary_edit_two_title": "What made you feel ",
  "diary_edit_three_title": "What emotions did you feel?",

  "diary_share_btn": "Share to Moments",
  "diary_share_save": "Save",
  "diary_share_wechat": "WeChat",
  "diary_share_no_wechat": "WeChat app not found!",
  "diary_share_xhs": "Red Note",
  "diary_share_no_xhs": "Red Note app not found!",
  "diary_share_no_xhs_sdk": "Red Note SDK failed to initialize",
  "diary_share_save_success": "Saved successfully!",
  "diary_share_save_fail": "Save failed!",
  "diary_share_no_granted": "Please allow Photos access to save images",

  ///==============drink task=====================
  "task_title": "Drink water",
  "task_unit": "cups",
  "task_today_complete": "today",
  "task_target": "Goal",
  "task_share_btn": "Share my day",
  "task_drink_time": "Today’s drinking times",

  "task_tips_one": "Xiaopa is a bit thirsty—have a sip!",
  "task_tips_two":
      "Hey, don’t forget to drink water. Taking care of yourself matters most.",
  "task_tips_three": "The cup is steaming—Xiaopa is waiting for the first sip.",
  "task_tips_four": "Water reservoir low—time to refill with a warm sip!",
  "task_tips_five": "Without water, Xiaopa’s little brain starts spinning~",
  "task_tips_six": "Have some water—be gentle to yourself and top me up too~",
  "task_tips_seven": "Storage dropping! Xiaopa needs a loving sip~",
  "task_tips_eight": "One warm sip, one small kindness—thank you~",
  "task_tips_nine": "Xiaopa is online and waiting—refuel time!",
  "task_tips_ten": "Take a water break. You’re doing great!",
  "task_tips_eleven": "I’m so thirsty I might hop to get your attention~",
  "task_tips_tweleve": "Don’t let me turn into a desert Pa—tap to help~",
  "task_tips_thirteen": "Both your body and Xiaopa need water—let’s go!",
  "task_tips_fourteen": "Water brings a healthy glow~",
  "task_tips_fifteen": "Beep—Xiaopa wants to hydrate with you!",
  "task_tips_sixteen":
      "Water is today’s gentlest ritual—Xiaopa raises the first cup~",
  "task_tips_seventeen": "Join me and drink water together~",
  "task_tips_eighteen": "A cup of water can soothe tiny tired feelings~",
  "task_tips_nineteen": "After this sip, everything feels just right~",
  "task_tips_twenty": "I’m drying up—come save me!",

  "task_success_tips_one": "Yay—reservoir full and energy online!",
  "task_success_tips_two": "Success! Xiaopa is blinking happily~",
  "task_success_tips_three": "Warm water delivered—I feel your care!",
  "task_success_tips_four": "+1 to health, +100 to thoughtfulness~",
  "task_success_tips_five": "Tanks filled—your warmth reached me~",
  "task_success_tips_six": "Water lovers are my favorite!",
  "task_success_tips_seven": "Sparkly, dewy Xiaopa unlocked~",
  "task_success_tips_eight": "You nailed this little habit—nice!",
  "task_success_tips_nine": "That sip powers today’s energy!",
  "task_success_tips_ten": "Water in, body relaxed—Xiaopa is even happier!",
  "task_success_tips_eleven": "Respect! You’re a hydration legend in my heart~",
  "task_success_tips_tweleve": "Awesome—Xiaopa quietly gave you a point~",
  "task_success_tips_thirteen": "Water full, energy up—keep going for health~",
  "task_success_tips_fourteen": "Thanks for that sip—back to full power!",
  "task_success_tips_fifteen": "Okay, you win—hail to the Water Queen.",
  "task_success_tips_sixteen":
      "You took hydration seriously today—waving a tiny flag for you!",
  "task_success_tips_seventeen":
      "Reservoir full—let’s stay energized together!",
  "task_success_tips_eighteen": "Done—today’s rhythm feels just right.",
  "task_success_tips_nineteen":
      "Life goes on—and I’ll remember this gentle moment.",
  "task_success_tips_twenty":
      "Hydration queen online—Xiaopa clings to your leg!",
  "task_success_tips_tweenty_one":
      "This cup warmed your tummy and Xiaopa’s heart~",

  "task_drink_error_tips":
      "You’ve logged water today—can’t change the goal now",

  "task_count_sheet_title": "Change daily water goal",
  "task_count_sheet_unit": "Unit: cups",
  "task_count_sheet_cancel": "Cancel",
  "task_count_sheet_confirm": "Confirm",
  "task_record_item_unit": "cups",

  "task_achievement_title": "Achievements",
  "task_achievement_explain": "Description",
  "task_achievement_unit": "days",
  "task_achievement_title_one": "Stay hydrated",
  "task_achievement_title_two": "Drink log",
  "task_achievement_title_three": "Collected %s Xiaopa",
  "task_achievement_btn": "Save skin",

  "task_achievement_unlock": "Locked",
  "task_achiebement_locked_title": "Unlocked",
  "task_achiebement_locked_tips_one": "Xiaopa tiles",
  "task_achiebement_btn": "My achievements",

  "task_detail_tips_one": "Glow up with hydration",
  "task_detail_tips_two": "Celebrate!",

  "task_detail_tisps_three": "Weekday",

  "task_detail_day": "days",
  "task_detail_day_one": "Past 7 days",
  "task_detail_week_one": "Daily average\nPast 4 weeks",
  "task_detail_year_one": "Daily average\nPast 12 months",

  "task_explan_tips_one": "Skin unlock rules",
  "task_explan_tips_two": "Streak rewards:",
  "task_explan_content_tips_two":
      "Starting from your 3rd consecutive day meeting your water goal, Xiaopa secretly prepares a ‘growth gift’. Every 7 days you unlock a new skin! For example, on day 3 you unlock the cute ‘Xiaopa One Dot’ skin; on day 10, the cool ‘Xiaopa Two Dots’.",
  "task_explan_tips_three": "If your streak breaks:",
  "task_explan_content_tips_three":
      "If you miss a day, your next drinking day restarts the count from day 1. No rush—Xiaopa will stay with you, step by step. Tip: enable reminders so Xiaopa can gently ‘nudge’ you to drink on time~",

  "task_explan_tips_four": "Valid logging time:",
  "task_explan_content_tips_four":
      "Each day from 00:00–23:59 counts for water logging. Meet your goal within this time to record a successful day. Don’t stress the numbers—what matters is that you’re caring for yourself.",

  "task_setting_title": "Settings",
  "task_setting_target": "Water goal",
  "task_setting_target_tips":
      "The default daily intake is 8 cups (≈2000 ml), based on medical and health guidance. Most people meet daily needs with this amount. If you exercise a lot or are in heat, increase it. If you have kidney issues or special conditions, follow medical advice.",
  "task_setting_num": "Goal amount",
  "task_setting_remind": "Reminders",
  "task_setting_hour": "Hour",
  "task_setting_interval": "Reminder interval",
  "task_setting_time": "Reminder times",
  "task_setting_start_time": "Start time",
  "task_setting_end_time": "End time",
  "task_setting_btn": "Save changes",
  "task_setting_unit_secend": "m",
  "task_setting_unit_hour": "h",

  "task_share_one": "Water streak:",
  "task_share_two": "Total cups consumed:",

  ///==============mine======================
  "mine_one": "Valid until:",
  "mine_two": "items",
  "mine_item_one": "My Xiaopa",
  "mine_item_two": "Settings",
  "mine_item_three": "User Agreement",
  "mine_item_four": "Privacy Policy",
  "mine_item_five": "User Guide",
  "mine_item_six": "Feedback & complaints",
  "mine_recharge_tips": "Receipt cannot be empty!",

  "mine_update_title": "Version update",
  "mine_update_btn": "Update now",

  "mine_device_empty_tips": "No Xiaopa yet~",
  "mine_device_one": "Valid until:",

  "mine_feedback_custom": "Contact support",
  "mine_feedback_custom_btn": "Contact now",
  "mine_feedback_type": "Feedback type",
  "mine_feedback_type_one": "Idea",
  "mine_feedback_type_two": "Device issue",
  "mine_feedback_type_three": "Report",
  "mine_feedback_type_four": "Other",
  "mine_feedback_detail": "Details",
  "mine_feedback_hint_text": "Please enter at least 6 characters",
  "mine_feedback_btn": "Submit",
  "mine_feedback_custom_tips":
      "Your WeChat doesn’t support customer service—please update WeChat!",
  "mine_feedback_complete": "Submitted %s, thank you so much",

  "mine_info_title": "Personal info",
  "mine_info_name": "Nickname",
  "mine_info_name_edit": "Edit nickname",
  "mine_info_name_hint_text": "Enter a new nickname",
  "mine_info_gender": "Gender",
  "mine_info_gender_man": "Male",
  "mine_info_gender_woman": "Female",
  "mine_info_gender_edit": "Change gender",
  "mine_info_save": "Save",
  "mine_info_birthday": "Birthday",
  "mine_info_birthday_tips": "Add your birthday for surprises",
  "mine_info_birthday_edit_tips":
      "Birthdays affect user benefits and can’t be changed later",
  "mine_info_birthday_dialog_title": "Confirm change",
  "mine_info_birthday_dialog_content":
      "After submitting, your birthday cannot be modified. Continue?",
  "mine_info_birthday_dialog_btn": "Change",
  "mine_info_phone": "Phone",
  "mine_photo_update_fail": "Image upload failed!",

  "mine_phone_Title": "Change phone",
  "mine_phone_tips": "After changing, your Xiaopa data moves to the new number",
  "mine_phone_old": "Current number",
  "mine_phone_new": "New phone number",
  "mine_phone_btn": "Change number",

  "mine_setting_language": "Language",
  "mine_setting_language_tips": "English",
  "mine_setting_change": "Change",

  "mine_setting_cancel": "Write Off",
  "mine_setting_cancel_tips":
      "This action is irreversible—proceed with caution",

  "mine_setting_pwd": "Account security",
  "mine_setting_version": "Xiaopa app updates",
  "mine_setting_logout": "Log out",

  "mine_setting_reset_app":
      "This change requires an app restart to take effect",
  "mine_setting_reset_title": "Restart app",
  "mine_setting_reset_tips": "Tap to reopen the app",

  "mine_setting_login_out_title": "Confirm logout",
  "mine_setting_login_out_content": "You will sign out of the current account",
  "mine_setting_login_out_btn": "Log out",

  "mine_setting_update_tips": "App is already up to date",

  "mine_cancel_title": "Account deletion agreement",
  "mine_cancel_tips": "I have read and agree to the Account Deletion Agreement",
  "mine_cancel_send_verify_tips":
      "To continue, complete SMS verification by entering the code we sent",
  "mine_cancel_verify_tips": "SMS code sent to",
  "mine_cancel_btn": "Delete account",
  "mine_cancel_explan_tips": '''Dear user:\n
Before deleting your account, please read the following terms carefully. This agreement explains your rights and obligations during and after deletion, and our rules for providing the deletion service.\n\n
You must ensure the account you request to delete was registered by you and you have the right to delete it.\n
Your account must have no unpaid fees or pending transactions.\n\n
Your account must have no unused balances, points, coupons, or you agree to forfeit them.\n
Once deleted, you will no longer be able to log in with this account or use any services in the app.\n
After deletion, your personal information and all data/records generated by the account will be deleted or anonymized and cannot be recovered. Even if you later register again with the same phone number or ID, it will be a new, independent account with no link to past data.\n
Account deletion is irreversible. Once completed, it cannot be undone.''',
  "mine_cancel_verify": "Resend in %s",
  "mine_cancel_error_title": "Notice",
  "mine_cancel_error_tips":
      "Please unbind Xiaopa before deleting your account~",
  "mine_cancel_success": "Deleted successfully",
  "mine_cancel_success_tips":
      "Your account has been deleted and all history cleared.\nIf you call Xiaopa again, you’ll need to register and log in anew~",

  "mine_pwd_title": "Change password",
  "mine_pwd_phone": "Phone number",
  "mine_pwd_new": "New password",
  "mine_pwd_new_hint_text": "Enter a new password",
  "mine_pwd_confirm": "Confirm password",
  "mine_pwd_confirm_hint_text": "Re‑enter the new password",
  "mine_pwd_new_tips": "8–16 chars, with letters & numbers",
  "mine_pwd_verify": "Verification code",
  "mine_pwd_verify_hint_text": "Enter the code",
  "mine_pwd_error_tips": "Passwords do not match!",

  ///==============recharge============================
  "recharge_title": "PaPa Coins",
  "recharge_unit": "coins",
  "recharge_tips": "Limited‑time offer",
  "recharge_pay_type": "Payment method",
  "recharge_pay_btn": "Pay now",
  "recharge_pay_error_one": "Please select a package to recharge",
  "recharge_pay_error_two": "Please select a payment method",
  "recharge_explain": "PaPa Coins Benefits",
  "recharge_ratio": "Conversion rate",
  "recharge_ratio_tips_one": "Current rate: 1 CNY = 100 PaPa Coins",
  "recharge_ratio_tips_two":
      "Recharge amounts are non‑refundable in parts. Please confirm before purchase",
  "recharge_use_title_explain": "Usage rules",
  "recharge_use_explain_tips_one":
      "Coins can be used in‑app only and are not withdrawable",
  "recharge_use_explain_tips_two": "Coins can be accumulated and used together",
  "recharge_symbol": "¥",

  "recharge_record": "PaPa Coin history",
  "recharge_record_one": "Available PaPa Coins:",

  ///=================storage======================
  "file_store_tips": "Please allow permission to save files",

  ///============utils======================
  "buy_engine_error_one": "Cannot connect to Apple Store",
  "buy_engine_error_two": "The selected package does not exist—contact support",
  "buy_engine_error_three": "Purchase failed, %s",
  "buy_engine_error_four": "Insufficient package quantity—contact support",
  "file_util_error_one": "Cannot open file",
  "file_util_error_two": "Failed to open file",
  "file_util_error_three": "Failed to select file",
  "permission_util_micro_title": "Microphone permission",
  "permission_util_micro_content":
      "Please allow microphone access to recognize your voice and interact with Xiaopa",
  "permission_util_micro_title_one": "Allow microphone access",
  "permission_util_bluetooth_title": "Bluetooth permission",
  "permission_util_bluetooth_content":
      "Please allow Bluetooth to scan and connect to nearby Xiaopa devices for setup",
  "permission_util_bluetooth_title_one": "Allow Bluetooth access",
  "permission_util_location_title": "Location permission",
  "permission_util_location_content":
      "Please allow Location to help Bluetooth discover nearby Xiaopa devices for setup",
  "permission_util_location_title_one": "Allow location access",
  "permission_util_media_title": "Photos permission",
  "permission_util_media_content":
      "Please allow Photos access to pick an avatar from your gallery",
  "permission_util_media_title_one": "Allow Photos access",
  "permission_util_notification_title": "Notifications permission",
  "permission_util_notification_content":
      "Please allow notifications so we can remind you in time",
  "permission_util_notification_title_one": "Enable notifications",
  "permission_overlay_btn": "Open settings",

  "time_util_day": "day",
  "time_util_hour": "hour",
  "time_util_minute": "min",
  "time_util_second": "sec",

  ///================widget==================
  "count_down_btn": "Skip",
  "common_dialog_title": "Notice",
  "common_dialog_confirm": "Confirm",
  "common_dialog_cancel": "Cancel",
  "common_dialog_hint_text": "Please enter"
};
