import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/pages/period/widgets/period_status_bottom.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class PeriodController extends BaseCommonController {
  PeriodController();

  bool isSelect = false;

  List<int> itemOneList = [];
  int sustainDay = 6;
  int sustainSelectDay = 6;

  List<int> itemTwoList = [];
  int intervalDay = 17;
  int intervalSelectDay = 17;

  DateTime sDate = DateTime.now();

  bool isPeriod = false;

  ///经期的天数
  int periodDay = 1;

  ///经期距离结束天数
  int periodLeveaDay = 1;

  ///距离下次经期到来天数
  int periodNextDay = 1;

  String periodStatus = '';

  String oneString = '';

  PeriodItemModelRecords periodItemModelRecords = PeriodItemModelRecords();

  PeriodCycleInfoModel cycleInfoModel = PeriodCycleInfoModel();
  List<PeriodCycleItemModel> periodCycleModelItems = [];

  ///周期历史widget
  ///上次
  String lastTimeDay = "";
  int lastTimeExpectedDay = 0;

  ///上上次
  String bLastTimeDay = "";
  int bLastTimeRealDay = 0;
  int bLastTimeExpectedDay = 0;

  @override
  void initData() async {
    isSelect = PeriodStore.to.isSelect;
    if (isSelect) {
      await PeriodStore.to.persiodSubmitInfo();
      isSelect = PeriodStore.to.isSelect;
    }

    if (isSelect) {
      for (var i = 3; i <= 15; i++) {
        itemOneList.add(i);
      }

      for (var i = 17; i <= 60; i++) {
        itemTwoList.add(i);
      }

      netState = NetState.dataSuccessState;
      update(["period"]);
    } else {
      _initData();
    }
  }

  @override
  void onHidden() {}

  _initData() async {
    oneString = TimeUtil.getLocaleTimeWithWeek(DateTime.now(), isWeekDay: true);

    await PeriodStore.to.persionCycleInfo();
    cycleInfoModel = PeriodStore.to.periodCycleInfoModel;

    isPeriod = !cycleInfoModel.periodIsEnd!;

    PeriodUtils.getPersionColor(model: cycleInfoModel, cTime: DateTime.now());
    
    periodDay = cycleInfoModel.dayInPhase ?? 1;
    periodNextDay = cycleInfoModel.daysUntilNextPeriod ?? 1;
    if (isPeriod) {
      periodLeveaDay = cycleInfoModel.daysLeftInPhase ?? 1;
    }

    await PeriodStore.to.periodItem(isEnd: false);
    periodItemModelRecords = PeriodStore.to.periodItemModelRecords;
    periodStatus =
        '${PeriodUtils.getStatusColorStr(int.parse(periodItemModelRecords.color ?? '0'))} ${PeriodUtils.getStatusFluxStr(int.parse(periodItemModelRecords.flow ?? '0'))} ${PeriodUtils.getStatusPainStr(int.parse(periodItemModelRecords.pain ?? '0'))}';

    periodCycleModelItems = await PeriodStore.to.periodCycleItems();
    if (periodCycleModelItems.length >= 2) {
      lastTimeDay = '${periodCycleModelItems[0].period!.realDuration}';
      lastTimeExpectedDay = periodCycleModelItems[0].expectedDuration ?? 0;

      DateTime sBLastTime =
          TimeUtil.parseIsoDate(periodCycleModelItems[1].period!.startDate!);
      DateTime eBLastTime =
          TimeUtil.parseIsoDate(periodCycleModelItems[1].period!.endDate!);
      bLastTimeDay =
          '${TimeUtil.getLocaleTimeWithWeek(sBLastTime)} - ${TimeUtil.getLocaleTimeWithWeek(eBLastTime)}';
      bLastTimeRealDay = periodCycleModelItems[1].period!.realDuration ?? 0;
      bLastTimeExpectedDay = periodCycleModelItems[1].expectedDuration ?? 0;
    } else if (periodCycleModelItems.length == 1) {
      lastTimeDay = '${periodCycleModelItems[0].period!.realDuration}';
      lastTimeExpectedDay = periodCycleModelItems[0].expectedDuration ?? 0;
    }

    netState = NetState.dataSuccessState;
    update(["period"]);
  }

  selectOneAction() {
    if (sustainSelectDay == 0) return;
    Get.to(() => SettingTwo());
  }

  selectTwoAction() {
    if (intervalSelectDay == 0) return;
    Get.to(() => SettingThree());
  }

  selectThreeAction() {
    Get.to(() => SettingComplete());
  }

  selectCompleteAction() async {
    await PeriodStore.to.persiodSubmitCreate(
        cycle: intervalSelectDay,
        period: sustainSelectDay,
        time: TimeUtil.toIso8601String(sDate));
    await PeriodStore.to.persiodSubmitInfo();
    isSelect = PeriodStore.to.isSelect;
    if (!isSelect) {
      Get.until((route) => Get.currentRoute == AppRoutes.PERIOD);

      ///请求数据
      _initData();
    }
    update(["period"]);
  }

  toHistoryPage() {
    Get.toNamed(AppRoutes.PERIOD_SETTING);
  }

  toAnalysisPage() {
    Get.toNamed(AppRoutes.PERIOD_ANALYSIS);
  }

  isPeriodAction(value) async {
    isPeriod = value;
    String sDTime = TimeUtil.toIso8601String(DateTime.now(), isZero: true);
    await PeriodStore.to.periodCycleRecord(
        cycleId: cycleInfoModel.cycleId ?? '',
        periodId: cycleInfoModel.periodId ?? '',
        date: sDTime,
        isStart: isPeriod);
    _initData();
  }

  periodStatusaction() {
    Get.bottomSheet(PeriodStatusBottom(
      onTap: (color, flux, pain) async {
        String sDTime = TimeUtil.toIso8601String(DateTime.now(), isZero: true);
        bool flag = await PeriodStore.to.periodRecordCreate(
            periodId: cycleInfoModel.periodId ?? "",
            date: sDTime,
            color: color,
            flux: flux,
            pain: pain,
            dayNumer: cycleInfoModel.dayInPhase ?? 1);
        if (flag) {
          periodStatus =
              '${PeriodUtils.getStatusColorStr(color)} ${PeriodUtils.getStatusFluxStr(flux)} ${PeriodUtils.getStatusPainStr(pain)}';

          update(["period"]);
        }
      },
    ), isScrollControlled: true);
  }
}
