import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'index.dart';

class PeriodAnalysisPage extends BaseCommonView<PeriodAnalysisController> {
  PeriodAnalysisPage({super.key});

  @override
  String? get navTitle => "period_analysis_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return SizedBox(
      width: 1.sw,
      height: 1.sh,
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: 10.h),
            _oneWidget(),
            SizedBox(height: 10.h),
            _twoWidget(),
            SizedBox(height: 10.h),
            _threeWidget(),
            SizedBox(height: 10.h),
            _fourWidget(),
            SizedBox(height: 10.h),
            _fiveWidget(),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  Widget _oneWidget() {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      width: 345.w,
      height: 253.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 10,
              // 阴影模糊程度
              offset: const Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: Column(
        children: [
          SizedBox(height: 14.h),
          Row(
            children: [
              Text(
                'period_analysis_one_widget_one'.tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700),
              ),
              SizedBox(width: 4.w),
              controller.isOnTime
                  ? Container(
                      padding: EdgeInsets.only(left: 4.w, right: 4.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3.r),
                          color: const Color.fromRGBO(97, 225, 177, 1)),
                      child: Center(
                        child: Text(
                          'period_analysis_one_widget_two'.tr,
                          style: StyleConfig.otherStyle(
                              color: Colors.white, fontSize: 10),
                        ),
                      ),
                    )
                  : const SizedBox(),
              const Expanded(child: SizedBox()),
              Images(
                path: R.mine_right_png,
                width: 9.w,
                height: 16.h,
              )
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Text(
                'period_analysis_one_widget_three'
                    .trArgs(['${controller.cycleDay}']),
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700),
              ),
              SizedBox(width: 7.w),
              Text(
                'period_analysis_one_widget_four'
                    .trArgs([controller.sPeriodDate, controller.ePeriodDate]),
                style: StyleConfig.otherStyle(
                    color: ColorConfig.authenticationTextColor,
                    fontWeight: FontWeight.w400,
                    fontSize: 14),
              ),
            ],
          ),
          SizedBox(height: 9.h),
          SizedBox(
            height: 14.h,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: List.generate(controller.cycleDay, (index) {
                return Container(
                  margin: EdgeInsets.only(left: index > 0 ? 2.w : 0),
                  width: 10.w,
                  height: 14.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.r),
                      color: const Color.fromRGBO(238, 238, 238, 1)),
                  child: index < controller.realDay
                      ? Container(
                          width: 8.r,
                          height: 8.r,
                          decoration: const BoxDecoration(
                            color: Color.fromRGBO(254, 139, 172, 1),
                            shape: BoxShape.circle,
                          ),
                        )
                      : const SizedBox(),
                );
              }),
            ),
          ),
          SizedBox(height: 16.h),
          Container(
            width: 315.w,
            height: 1.h,
            color: const Color.fromRGBO(233, 232, 233, 1),
          ),
          Container(
              margin: EdgeInsets.only(top: 14.h),
              width: 320.w,
              height: 113.h,
              child: Stack(
                children: [
                  Positioned(
                    top: 5.h,
                    right: 14.w,
                    child: Container(
                      width: 80.w,
                      height: 108.h,
                      color: const Color.fromRGBO(255, 245, 247, 1),
                    ),
                  ),
                  Positioned(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Images(
                              path: R.period_analysis_image_01_png,
                              width: 18.w,
                              height: 23.h,
                            ),
                            SizedBox(width: 5.w),
                            Text(
                              'period_analysis_one_widget_five'.tr,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontSize: 14),
                            ),
                            const Expanded(child: SizedBox()),
                            Text(
                              'period_analysis_one_widget_six'.tr,
                              style: StyleConfig.otherStyle(
                                  color: const Color.fromRGBO(255, 80, 110, 1),
                                  fontSize: 10),
                            ),
                            SizedBox(width: 12.w),
                          ],
                        )
                      ],
                    ),
                  ),
                  ...List.generate(
                    controller.oneTypeList.length,
                    (index) {
                      return Positioned(
                        top: 33.h + index * 30.h,
                        child: OneItem(
                          date: controller.oneTypeList[index].date,
                          tip: controller.oneTypeList[index].beforeTips,
                          pColor: controller.oneTypeList[index].pColor,
                          pValue: controller.oneTypeList[index].pValue,
                          cDaysTip: controller.oneTypeList[index].cDaysTip,
                        ),
                      );
                    },
                  ),
                ],
              ))
        ],
      ),
    ).inkWell(() => controller.periodCycle());
  }

  Widget _twoWidget() {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      width: 345.w,
      height: 202.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 10,
              // 阴影模糊程度
              offset: const Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: Column(
        children: [
          SizedBox(height: 14.h),
          Row(
            children: [
              Text(
                'period_analysis_two_widget_one'.tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700),
              ),
              const Expanded(child: SizedBox()),
              Images(
                path: R.mine_right_png,
                width: 9.w,
                height: 16.h,
              )
            ],
          ),
          SizedBox(height: 11.h),
          Row(
            children: [
              Images(
                path: R.period_analysis_image_01_png,
                width: 18.w,
                height: 23.h,
              ),
              SizedBox(width: 5.w),
              Text(
                'period_analysis_two_widget_two'.tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor, fontSize: 14),
              ),
              SizedBox(width: 5.w),
              Text(
                'period_analysis_one_widget_three'
                    .trArgs(['${controller.twoReadDay}']),
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w700),
              ),
              const Expanded(child: SizedBox()),
              Text(
                'period_analysis_two_widget_three'.tr,
                style: StyleConfig.otherStyle(
                    color: const Color.fromRGBO(255, 80, 110, 1), fontSize: 10),
              ),
              SizedBox(width: 12.w),
            ],
          ),
          SizedBox(height: 15.h),
          controller.twoTypeList.isNotEmpty
              ? TwoItem(twoTypeList: controller.twoTypeList)
              : const SizedBox()
        ],
      ),
    ).inkWell(() => controller.periodDay());
  }

  Widget _threeWidget() {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      width: 345.w,
      height: 167.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 10,
              // 阴影模糊程度
              offset: const Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: Column(
        children: [
          SizedBox(height: 14.h),
          Row(
            children: [
              Text(
                'period_analysis_three_widget_one'.tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700),
              ),
              const Expanded(child: SizedBox()),
              Images(
                path: R.mine_right_png,
                width: 9.w,
                height: 16.h,
              )
            ],
          ),
          SizedBox(height: 17.h),
          controller.threeTypeList.isNotEmpty
              ? ThreeItem(threeTypeList: controller.threeTypeList)
              : const SizedBox(),
          SizedBox(height: 10.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Images(
                path: R.period_analysis_three_image_png,
                width: 20.r,
                height: 20.r,
              ),
              SizedBox(width: 3.w),
              Text(
                'period_analysis_three_widget_two'.tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.shopDetailTextColor, fontSize: 10),
              )
            ],
          )
        ],
      ),
    ).inkWell(() => controller.periodColor());
  }

  Widget _fourWidget() {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      width: 345.w,
      height: 258.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 10,
              // 阴影模糊程度
              offset: const Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: Column(
        children: [
          SizedBox(height: 14.h),
          Row(
            children: [
              Text(
                'period_analysis_four_widget_one'.tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700),
              ),
              const Expanded(child: SizedBox()),
              Images(
                path: R.mine_right_png,
                width: 9.w,
                height: 16.h,
              )
            ],
          ),
          SizedBox(height: 20.h),
          ChartWidget(
            painLevels: controller.fourPainLevels, // 0表示无数据显示虚线
            dayLabels: controller.fourDayLabels,
            statusLabels: controller.fourStatusLabels,
          ),
          SizedBox(height: 10.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 54.w,
                height: 20.h,
                child: Row(
                  children: [
                    Images(
                      path: R.period_analysis_image_02_png,
                      width: 20.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      'period_analysis_four_widget_two'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor, fontSize: 10),
                    )
                  ],
                ),
              ),
              SizedBox(width: 26.w),
              SizedBox(
                width: 54.w,
                height: 20.h,
                child: Row(
                  children: [
                    Images(
                      path: R.period_analysis_image_03_png,
                      width: 20.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      'period_analysis_three_widget_two'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor, fontSize: 10),
                    )
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    ).inkWell(() => controller.periodFlux());
  }

  Widget _fiveWidget() {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      width: 345.w,
      height: 258.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 10,
              // 阴影模糊程度
              offset: const Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: Column(
        children: [
          SizedBox(height: 14.h),
          Row(
            children: [
              Text(
                'period_analysis_five_widget_one'.tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700),
              ),
              const Expanded(child: SizedBox()),
              Images(
                path: R.mine_right_png,
                width: 9.w,
                height: 16.h,
              )
            ],
          ),
          SizedBox(height: 20.h),
          ChartWidget(
            painLevels: controller.fivePainLevels, // 0表示无数据显示虚线
            dayLabels: controller.fiveDayLabels,
            statusLabels: controller.fiveStatusLabels,
          ),
          SizedBox(height: 10.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 54.w,
                height: 20.h,
                child: Row(
                  children: [
                    Images(
                      path: R.period_analysis_image_02_png,
                      width: 20.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      'period_analysis_four_widget_two'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor, fontSize: 10),
                    )
                  ],
                ),
              ),
              SizedBox(width: 26.w),
              SizedBox(
                width: 54.w,
                height: 20.h,
                child: Row(
                  children: [
                    Images(
                      path: R.period_analysis_image_03_png,
                      width: 20.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      'period_analysis_three_widget_two'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor, fontSize: 10),
                    )
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    ).inkWell(() => controller.periodPain());
  }

  @override
  Widget buildContent() {
    return GetBuilder<PeriodAnalysisController>(
      init: PeriodAnalysisController(),
      id: "period_analysis",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
