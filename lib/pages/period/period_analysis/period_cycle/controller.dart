import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/pages/period/index.dart';

class PeriodCycleController extends BaseListController {
  PeriodCycleController();

  List<PeriodCycleItemModel> periodCycleModelItems = [];

  @override
  void initData() {
    _cycleData();
    netState = NetState.dataSuccessState;
    update(["cycle_record"]);
  }

  @override
  void onHidden() {}

  @override
  refreshData() {
    super.refreshData();
    page = 1;
    _cycleData();
  }

  @override
  loadMore() {
    super.loadMore();
    page++;
    _cycleData();
  }

  _cycleData() async {
    if (page == 1) {
      periodCycleModelItems.clear();
      update(["cycle_record"]);
      periodCycleModelItems = await PeriodStore.to.periodCycleItems(page: page);
      refreshController.refreshCompleted();
    } else {
      List<PeriodCycleItemModel> list =
          await PeriodStore.to.periodCycleItems(page: page);
      periodCycleModelItems.addAll(list);
      refreshController.loadComplete();
    }
    if (periodCycleModelItems.length < 10) {
      refreshController.loadNoData();
    }

    update(["cycle_record"]);
  }
}
