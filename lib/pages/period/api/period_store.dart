import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/storage/storage.dart';

class PeriodStore extends GetxService {
  static PeriodStore get to => Get.find();

  bool _isSelect = false;

  bool get isSelect => _isSelect;

  PeriodSubmitModel _periodSubmitModel = PeriodSubmitModel();

  PeriodSubmitModel get periodSubmitModel => _periodSubmitModel;

  PeriodCycleInfoModel _periodCycleInfoModel = PeriodCycleInfoModel();

  PeriodCycleInfoModel get periodCycleInfoModel => _periodCycleInfoModel;

  final List<PeriodItemModel> _periodItemModelItems = [];

  PeriodItemModelRecords _periodItemModelRecords = PeriodItemModelRecords();
  PeriodItemModelRecords get periodItemModelRecords => _periodItemModelRecords;

  final List<PeriodCycleItemModel> _periodCycleModelItems = [];

  @override
  void onInit() {
    super.onInit();
    persiodSubmitInfo();
  }

  ///获取经期信息
  persiodSubmitInfo() async {
    Result result = await http.periodSubmitInfo({});
    if (result.code == 0) {
      _periodSubmitModel = result.data;
      _isSelect = false;
      if (_periodSubmitModel.id!.isEmpty) {
        _isSelect = true;
      }
    }
  }

  ///提交经期
  persiodSubmitCreate(
      {required int cycle, required int period, required String time}) async {
    CToast.showLoading();
    var map = {
      "menstrual_cycle_duration": cycle,
      "menstrual_period_duration": period,
      "last_menstrual_date": time
    };
    Result result = await http.periodSubmitCreate(map);
    if (result.code == 0) {
      _persiodFirstCreate();
    }
  }

  _persiodFirstCreate() async {
    var map = {"user_id": UserStore.to.userInfo.id};
    Result result = await http.periodCycleCreate(map);
    if (result.code == 0) {}
  }

  ///获取当前周几各时期信息
  persionCycleInfo() async {
    Result result = await http.periodCycleInfo({});
    if (result.code == 0) {
      _periodCycleInfoModel = result.data;
    }
  }

  ///记录经期
  periodCycleRecord(
      {required String cycleId,
      required String periodId,
      required String date,
      required bool isStart}) async {
    if (cycleId.isEmpty || periodId.isEmpty) return;
    CToast.showLoading();
    var map = {
      "cycle_id": cycleId,
      "period_id": periodId,
      "date": date,
      "is_start": isStart
    };
    Result result = await http.periodCycleRecord(map);
    if (result.code == 0) {}
  }

  ///记录月经情况
  Future<bool> periodRecordCreate(
      {required String periodId,
      required String date,
      required int color,
      required int flux,
      required int pain,
      required int dayNumer}) async {
    if (periodId.isEmpty) return false;
    CToast.showLoading();
    var map = {
      "period_id": periodId,
      "date": date,
      "color": color,
      "flow": flux,
      "pain": pain,
      "day_number": dayNumer
    };
    Result result = await http.periodRecordCreateOrUpdate(map);
    if (result.code == 0) {
      return true;
    }
    return false;
  }

  ///月经情况列表
  ///[isAll] 是否获取全部数据
  ///[isEnd] 是否结束，首页情况查看未结束获取
  periodItem({int page = 1, bool isAll = false, bool isEnd = true}) async {
    var map = RequestBody(pageNum: page, filters: [
      isAll ? null : Filter(expr: "=", name: "is_end", value: isEnd)
    ], orders: [
      Order(expr: "desc", name: "created_at")
    ]).toJson();
    Result result = await http.periodItem(map);
    _periodItemModelItems.clear();
    if (result.code == 0) {
      for (PeriodItemModel model in result.data.data!) {
        _periodItemModelItems.add(model);
      }

      ///获取最新月经情况
      if (page == 1 && !isEnd) {
        if (_periodItemModelItems.isEmpty) return;
        if (_periodItemModelItems[0].records![0] != null &&
            _periodItemModelItems[0].records!.isNotEmpty) {
          _periodItemModelRecords = _periodItemModelItems[0].records![0]!;
        }
      }
    }

    return _periodItemModelItems;
  }

  ///周期列表
  ///[isAll] 是否获取全部数据
  ///[isEnd] 是否结束，首页情况查看未结束获取
  periodCycleItems(
      {int page = 1,
      bool isAll = false,
      bool isEnd = true,
      int pageSize = 10}) async {
    var map = RequestBody(pageNum: 1, pageSize: pageSize, filters: [
      isAll ? null : Filter(expr: "=", name: "is_end", value: true)
    ], orders: [
      Order(expr: "desc", name: "created_at")
    ]).toJson();
    Result result = await http.periodCycleItem(map);
    _periodCycleModelItems.clear();
    if (result.code == 0) {
      for (PeriodCycleItemModel model in result.data.data!) {
        _periodCycleModelItems.add(model);
      }
    }
    return _periodCycleModelItems;
  }
}
