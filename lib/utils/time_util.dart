import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/storage/config_store.dart';
import 'package:intl/intl.dart';

class TimeUtil {
  ///2024-05-14T10:31:23.555+08:00 转 2024-05-14 10:31:23
  ///isHMS 是否需要时分秒
  static String getSimpleDate(date,
      {bool isHMS = true, String splitStr = "."}) {
    String temp = date;
    if (temp.isEmpty) return '';
    temp = temp.split(splitStr)[0];
    temp = temp.replaceAll('T', ' ');
    if (!isHMS) {
      temp = temp.split(' ')[0];
    }
    return temp;
  }

  ///获取时分
  static String getSimpleHM(date) {
    String temp = getSimpleDate(date);
    temp = temp.split(' ')[1];
    temp = '${temp.split(':')[0]}:${temp.split(':')[1]}';
    return temp;
  }

  static String toIso8601String(DateTime tTime, {bool isZero = false}) {
    String isoString = tTime.toIso8601String();
    // 获取时区偏移 (+08:00)
    String offset = _formatTimeZoneOffset(tTime.timeZoneOffset);

    // 拼接成需要的格式
    String result = isoString.split('.').first + offset;
    if (isZero) {
      result = result.replaceFirst(RegExp(r'T.*\+'), 'T00:00:00+');
    }
    return result;
  }

  static String _formatTimeZoneOffset(Duration offset) {
    final hours = offset.inHours.abs().toString().padLeft(2, '0');
    final minutes = (offset.inMinutes.abs() % 60).toString().padLeft(2, '0');
    final sign = offset.isNegative ? '-' : '+';
    return '$sign$hours:$minutes';
  }

  ///[isWeekDay] 是否需要星期
  static String getLocaleTimeWithWeek(DateTime date,
      {bool isWeekDay = false, bool isEn = false}) {
    var df = DateFormat.Md(ConfigStore.to.locale.toString()).format(date);
    if (ConfigStore.to.getLocaleCode() == 'zh') {
      if (isWeekDay) {
        df = DateFormat('M月dd日 EEEE', 'zh_CN').format(date);
      } else {
        df = DateFormat('M月dd日', 'zh_CN').format(date);
      }
      if (isEn) {
        if (isWeekDay) {
          df = DateFormat('EEE M/d', 'en_US').format(date);
        } else {
          df = DateFormat('M/d', 'en_US').format(date);
        }
      }
    } else {
      if (isWeekDay) {
        df = DateFormat('EEE M/d', 'en_US').format(date);
      } else {
        df = DateFormat('M/d', 'en_US').format(date);
      }
    }

    return df;
  }

  static DateTime parseIsoDate(String isoDate) {
    DateTime dt = DateTime.parse(isoDate);
    return dt.toLocal();
  }

  /// 比较传入的 "HH:mm" 时间和当前时间
  /// 如果已过该时间，返回 true，否则 false
  static bool isTimePassed(String timeStr) {
    try {
      DateTime target;

      // 尝试解析完整日期时间格式
      if (timeStr.contains('-') || timeStr.contains('/')) {
        // 支持 2025-08-22 17:00:00 或 2025/08/22 17:00:00
        target = DateTime.parse(timeStr);
      } else {
        // 解析 HH:mm 格式
        final parts = timeStr.split(":");
        if (parts.length != 2) return false;

        int hour = int.parse(parts[0]);
        int minute = int.parse(parts[1]);

        DateTime now = DateTime.now();
        target = DateTime(now.year, now.month, now.day, hour, minute);
      }

      DateTime now = DateTime.now();
      return now.isAfter(target);
    } catch (e) {
      return false; // 格式错误时返回 false
    }
  }

  ///格式化时间取时分
  static String getFormatDateHM(date) {
    DateTime dateTime = DateTime.parse(date).toLocal();
    return '${dateTime.hour.toString().padLeft(2, "0")}:${dateTime.minute.toString().padLeft(2, "0")}';
  }

  ///返回当前时间跟指定时间的时间差
  static String differenceTime(String orderTime) {
    ///当前时间戳
    var difference = DateTime.now()
        .difference(DateTime.parse(TimeUtil.getSimpleDate(orderTime)));
    var expiredDate = const Duration(hours: 0, minutes: 29, seconds: 59);
    return "${twoDigits(expiredDate.inHours - difference.inHours)}:${twoDigits(expiredDate.inMinutes - difference.inMinutes.remainder(60))}:${twoDigits(59 - difference.inSeconds.remainder(60))}";
  }

  static String twoDigits(int n) => n.toString().padLeft(2, '0');

  ///判断当前日期是否在这个日期段内
  ///结束时间不包含当天
  static bool isTimeInRange(
      {DateTime? cTime, DateTime? startTime, DateTime? endTime}) {
    final now = cTime ?? DateTime.now();
    // 只保留年月日，去掉时分秒
    DateTime normalize(DateTime dt) => DateTime(dt.year, dt.month, dt.day);

    final today = normalize(now);
    final start = normalize(startTime!);
    final end = normalize(endTime!);

    return !today.isBefore(start) && !today.isAfter(end);
  }

  ///判断今天是工作日还是周末
  static bool isWeekDay() {
    // 定义工作日的集合
    final weekdays = <int>{
      DateTime.monday,
      DateTime.tuesday,
      DateTime.wednesday,
      DateTime.thursday,
      DateTime.friday
    };

    DateTime now = DateTime.now();

    return weekdays.contains(now.weekday);
  }

  ///判断小时是否在范围内
  static bool isHourInRange(String sTime, String eTime) {
    DateTime now = DateTime.now();
    int nowHour = now.hour;
    int nowMin = now.minute;

    double temp = 0.0;

    if (nowMin >= 30) {
      temp = nowHour + 0.5;
    } else {
      temp = nowHour.toDouble() + 0.1;
    }

    double dSTime = double.parse(sTime);
    double dETime = double.parse(eTime);

    // logD('本地时间：$temp = dSTime:$dSTime = dETime:$dETime');

    return temp > dSTime && temp <= dETime;
  }

  ///判断选择的时间要大于当前时间的outTime时间
  static bool isAfterNowPlusMinutes(String input, {int outTime = 3}) {
    final now = DateTime.now();
    final compareTime = now.add(Duration(minutes: outTime));

    DateTime? inputTime;

    // 判断格式
    if (RegExp(r'^\d{1,2}:\d{1,2}$').hasMatch(input)) {
      // 格式: HH:mm
      final parts = input.split(":");
      final hour = int.tryParse(parts[0]);
      final minute = int.tryParse(parts[1]);
      if (hour == null || minute == null) return false;

      inputTime = DateTime(now.year, now.month, now.day, hour, minute);

      // 如果小于 compareTime，当作第二天
      if (!inputTime.isAfter(compareTime)) {
        inputTime = inputTime.add(const Duration(days: 1));
      }
    } else {
      try {
        // 格式: yyyy-MM-dd HH:mm
        inputTime = DateTime.parse(input);
      } catch (_) {
        return false;
      }
    }

    return inputTime.isAfter(compareTime);
  }

  ///时间戳转时间格式
  static String getTimeStr(String timesTamp) {
    return DateTime.fromMillisecondsSinceEpoch(int.parse(timesTamp)).toString();
  }

  ///跟当前时间比较 返回时间差(聊天框)
  static String getTimeDifference(int timesTamp) {
    String temp = '';
    DateTime time = DateTime.fromMillisecondsSinceEpoch(timesTamp);

    DateTime now = DateTime.now();

    int differenceDay = now.difference(time).inDays;

    if (differenceDay == 0) {
      DateTime y = time.subtract(const Duration(days: 1));

      // logD(
      //     "subtract时间：$y  == ${y.day} == ${now.day} == ${time.difference(y).inDays}");
      if (now.day - y.day <= 1) {
        temp = '${time.hour}:${twoDigits(time.minute)}';
      } else {
        temp = "昨天";
      }
    } else if (differenceDay >= 1 && differenceDay < 365) {
      temp = '${time.month}月${time.day}日';
    } else {
      temp = '${time.year}-${time.month}-${time.day}';
    }
    return temp;
  }

  ///获取当前时间戳
  static String getTimesTamp() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// 秒转天时分秒
  static String second2DHMS(int sec) {
    String hms = "00天00时00分00秒";
    if (sec > 0) {
      int d = sec ~/ 86400;
      int h = (sec % 86400) ~/ 3600;
      int m = (sec % 3600) ~/ 60;
      int s = sec % 60;
      hms =
          "${twoDigits(d)}${"time_util_day".tr}${twoDigits(h)}${"time_util_hour".tr}${twoDigits(m)}${"time_util_minute".tr}${twoDigits(s)}${'time_util_second'.tr}";
    }
    return hms;
  }

  ///判断时间是否不超过2分钟 撤回消息用
  static bool hasElapsedTwoMinutes(String startTime) {
    if (startTime == '') return false;
    DateTime temp = DateTime.fromMillisecondsSinceEpoch(int.parse(startTime));
    final duration = DateTime.now().difference(temp);
    return duration.inMinutes < 2;
  }

  static String secondsToHMS(int seconds) {
    var duration = Duration(seconds: seconds);
    var hours = duration.inHours;
    var minutes = duration.inMinutes % 60;
    var secondsRemain = duration.inSeconds % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secondsRemain.toString().padLeft(2, '0')}';
  }

  /// 毫秒转分秒 音乐播放器使用
  static String secondsToMS(int milliSeconds) {
    var seconds = (milliSeconds / 1000).truncate();
    var minutes = (seconds / 60).truncate();
    seconds = seconds % 60;
    return '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  ///字符串转时间格式
  static TimeOfDay parseTimeString(String timeString) {
    // 确保时间字符串有正确的格式
    if (timeString.split(':').length != 2) {
      throw Exception('字符串格式必须是："HH:mm".');
    }

    // 解析小时和分钟
    int hour = int.parse(timeString.split(':')[0]);
    int minute = int.parse(timeString.split(':')[1]);

    // 创建TimeOfDay对象
    return TimeOfDay(hour: hour, minute: minute);
  }
}
