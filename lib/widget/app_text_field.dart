import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// 自定义 TextField：
/// - 自动过滤表情
/// - 保留原本 onChanged 回调
/// - 兼容所有 TextField 的属性
class AppTextField extends StatelessWidget {
  final ValueChanged<String>? onChanged;
  final TextEditingController? controller;
  final Color? cursorColor;
  final FocusNode? focusNode;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final TextStyle? style;
  final int? maxLines;
  final int? minLines;
  final bool obscureText;
  final bool autofocus;
  final bool readOnly;
  final List<TextInputFormatter>? inputFormatters;
  final TextCapitalization textCapitalization;
  final bool expands;
  final int? maxLength;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onSubmitted;

  const AppTextField({
    super.key,
    this.onChanged,
    this.controller,
    this.cursorColor,
    this.focusNode,
    this.decoration,
    this.keyboardType,
    this.textInputAction,
    this.style,
    this.maxLines = 1,
    this.minLines,
    this.obscureText = false,
    this.autofocus = false,
    this.readOnly = false,
    this.inputFormatters,
    this.textCapitalization = TextCapitalization.none,
    this.expands = false,
    this.maxLength,
    this.onEditingComplete,
    this.onSubmitted,
  });

  // 过滤掉 emoji
  String _filterEmoji(String input) {
    String filtered = input.replaceAll(
        RegExp(
            r'[\u{1F600}-\u{1F64F}'
            r'|\u{1F300}-\u{1F5FF}'
            r'|\u{1F680}-\u{1F6FF}'
            r'|\u{2600}-\u{26FF}'
            r'|\u{2700}-\u{27BF}]',
            unicode: true),
        '');

    // 如果设置了 maxLength，直接截断
    if (maxLength != null && filtered.length > maxLength!) {
      filtered = filtered.substring(0, maxLength);
    }
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      contextMenuBuilder: (context, editableTextState) {
        return AdaptiveTextSelectionToolbar.buttonItems(
          anchors: editableTextState.contextMenuAnchors,
          buttonItems: [
            ContextMenuButtonItem(
              onPressed: () {
                editableTextState.copySelection(SelectionChangedCause.toolbar);
              },
              type: ContextMenuButtonType.copy,
            ),
            ContextMenuButtonItem(
              onPressed: () {
                editableTextState.cutSelection(SelectionChangedCause.toolbar);
              },
              type: ContextMenuButtonType.cut,
            ),
            ContextMenuButtonItem(
              onPressed: () async {
                final clipText = await const MethodChannel('app/clipboard_read')
                    .invokeMethod<String>('get');
                if (clipText == null || clipText.isEmpty) return;

                // ✅ 这里直接用 Clipboard.getData
                final data = await Clipboard.getData(Clipboard.kTextPlain);

                if (data == null || data.text == null || data.text!.isEmpty) {
                  return;
                }

                final oldValue = editableTextState.textEditingValue;
                final selection = oldValue.selection;
                final insert = data.text!;

                final String newText = selection.isValid
                    ? oldValue.text
                        .replaceRange(selection.start, selection.end, insert)
                    : oldValue.text + insert;

                final int newOffset = selection.isValid
                    ? selection.start + insert.length
                    : newText.length;

                final newValue = TextEditingValue(
                  text: newText,
                  selection: TextSelection.collapsed(offset: newOffset),
                  composing: TextRange.empty,
                );

                // ✅ 通过 userUpdateTextEditingValue 更新，模拟用户输入
                editableTextState.userUpdateTextEditingValue(
                  newValue,
                  SelectionChangedCause.toolbar,
                );
              },
              type: ContextMenuButtonType.paste,
              label: 'paste'.tr,
            ),
            ContextMenuButtonItem(
              onPressed: () {
                editableTextState.selectAll(SelectionChangedCause.toolbar);
              },
              type: ContextMenuButtonType.selectAll,
            ),
          ],
        );
      },
      controller: controller,
      focusNode: focusNode,
      cursorColor: cursorColor,
      decoration: decoration,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      style: style,
      textAlign: TextAlign.start,
      maxLines: maxLines,
      minLines: minLines,
      obscureText: obscureText,
      autofocus: autofocus,
      readOnly: readOnly,
      inputFormatters: [
        if (inputFormatters != null) ...inputFormatters!,
        if (maxLength != null) LengthLimitingTextInputFormatter(maxLength),
        CustomInputFormatter(),
      ],
      textCapitalization: textCapitalization,
      expands: expands,
      onEditingComplete: onEditingComplete,
      onSubmitted: onSubmitted,
      onChanged: (value) {
        // 过滤 emoji
        String filtered = _filterEmoji(value);

        if (filtered != value) {
          controller?.text = filtered;
          controller?.selection = TextSelection.fromPosition(
            TextPosition(offset: filtered.length),
          );
        }

        // 先走全局逻辑
        // logD("统一 onChanged 拦截: $filtered");

        // 再调用原本传进来的 onChanged
        if (onChanged != null) {
          onChanged?.call(filtered);
        }
      },
    );
  }
}

class CustomInputFormatter extends TextInputFormatter {
  ///输入框只能输入中文英文数字符号
  /*
  •	\p{Sm} 数学符号（+ = < > × ÷ 等）
	•	\p{Sc} 货币符号（$ ¥ € 等）
	•	\p{Sk} 修饰符号（^ ~ ˚ 等）
	•	\p{So} 其他符号（比如 emoji、♠︎、♥︎、★ 等）
  */

  RegExp regExp =
      RegExp(r'[\u4e00-\u9fa5a-zA-Z0-9\p{P}\p{Sm}\s]+', unicode: true);

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // 过滤掉不符合正则的字符
    final filtered =
        newValue.text.split('').where((ch) => regExp.hasMatch(ch)).join();

    if (filtered != newValue.text) {
      return TextEditingValue(
        text: filtered,
        selection: TextSelection.collapsed(offset: filtered.length),
      );
    }
    return newValue;
  }
}

// static List<TextInputFormatter> tfInputFormatter(
//       {int? textLength, bool isDigitsOnly = false}) {
//     return [
//       if (isDigitsOnly) FilteringTextInputFormatter.digitsOnly,
//       LengthLimitingTextInputFormatter(textLength),
//       FilteringTextInputFormatter.deny(
//         // 常见 Emoji Unicode 区间
//         RegExp(
//           r'[\u{1F600}-\u{1F64F}]|' // 表情
//           r'[\u{1F300}-\u{1F5FF}]|' // 符号 & 图形
//           r'[\u{1F680}-\u{1F6FF}]|' // 交通 & 地图
//           r'[\u{2600}-\u{26FF}]|' // 杂项符号
//           r'[\u{2700}-\u{27BF}]|' // 字母符号
//           r'[\u{1F900}-\u{1F9FF}]|' // 补充符号和象形文字
//           r'[\u{1FA70}-\u{1FAFF}]|' // 符号和象形文字扩展
//           r'[\u{1F1E6}-\u{1F1FF}]|' // 国旗区域符号
//           r'[\u{1F3FB}-\u{1F3FF}]|' // 肤色修饰符
//           r'\u200D' // 零宽连接符
//           ,
//           unicode: true,
//         ),
//       )
//     ];
//   }
